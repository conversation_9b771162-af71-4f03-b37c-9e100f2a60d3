<template>
  <div :class="`__ci text-${dark ? 'white' : 'black'}`">
    <default-avatar :imageStyle="{ objectFit: 'contain' }" :dark="dark" :model-value="org" size-in="30px"></default-avatar>
    <div class="__t q-px-sm tw-six">
      {{ $limitStr(org?.name, 18, '...') }}
    </div>

    <q-icon size="15px" name="mdi-menu-down"></q-icon>

    <q-menu v-model="menu">
      <div class="_fw mw500 q-pa-sm __list">
        <q-input dense v-model="search.text">
          <template v-slot:prepend>
            <q-icon name="mdi-magnify"></q-icon>
          </template>
        </q-input>
        <q-list separator>
          <default-item
              v-for="(opt, i) in h$.data"
              :key="`opt-${i}`"
              :model-value="opt"
              @update:model-value="setOrg(opt)"
          ></default-item>
        </q-list>
        <div class="_fw q-pa-xs">
          <q-item dense clickable @click="addDialog = true">
            <q-item-section avatar>
              <q-icon color="primary" name="mdi-plus"></q-icon>
            </q-item-section>
            <q-item-section>
              <q-item-label>Add New</q-item-label>
            </q-item-section>
          </q-item>
        </div>
      </div>
    </q-menu>

    <common-dialog v-model="addDialog" setting="small">
      <org-edit-card @close="addDialog=false" editing @update:model-value="handleAdd"></org-edit-card>
    </common-dialog>
  </div>
</template>

<script setup>
  import DefaultAvatar from 'components/common/avatars/DefaultAvatar.vue';
  import DefaultItem from 'components/common/avatars/DefaultItem.vue';
  import OrgEditCard from 'components/orgs/cards/OrgEditCard.vue';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';

  import {idGet} from 'src/utils/id-get';
  import {useOrgs} from 'stores/orgs';
  import {computed, nextTick, ref, watch} from 'vue';
  import {LocalStorage} from 'symbol-auth-client';
  import {loginPerson} from 'stores/utils/login';
  import {HFind} from 'src/utils/hFind';
  import {useRouter} from 'vue-router';
  import {$limitStr, fakeId} from 'src/utils/global-methods';
  import {HQuery} from 'src/utils/hQuery';
  import {useEnvStore} from 'stores/env';
  import {contextItems} from 'layouts/utils/context-items';

  const envStore = useEnvStore()

  const { person } = loginPerson()

  const router = useRouter();

  const store = useOrgs();
  const props = defineProps({
    size: { default: '35px' },
    dark: { type: Boolean, default: false },
  })

  const { getOrgId } = contextItems(envStore)
  const { item: org } = idGet({
    store,
    value: getOrgId
  })

  const addDialog = ref(false);
  const menu = ref(false);

  const clearAll = () => {
    LocalStorage.removeItem('provider_id');
    LocalStorage.removeItem('care_account_id');
    envStore.setPlanId(undefined);
  }
  const setOrg = (val) => {
    if(val._id !== getOrgId.value) {
      envStore.setOrgId(val._id)
      clearAll();
      nextTick(() => router.go())
    }
  }
  const handleAdd = (o) => {
    envStore.setOrgId(o._id)
    clearAll()
    addDialog.value = false
  }

  const limit = ref(15);

  const pause = computed(() => (org.value._id && !menu.value) || !person.value?.inOrgs?.length)

  const { search, searchQ } = HQuery({})

  const { h$ } = HFind({
    store,
    limit: ref(10),
    pause,
    params: computed(() => {
      return {
        query: {
          ...searchQ.value,
          $or: [{_id: { $in: (person.value?.inOrgs || []).filter(a => !!a) }}, { 'updatedBy.login': person.value.login || fakeId }]
        }
      }
    })
  })


  watch(person, (nv) => {
    if (nv) {
      setTimeout(() => {
      limit.value = nv.inOrgs?.length
      if(!envStore.getOrgId && nv.inOrgs?.length) envStore.setOrgId((nv.inOrgs || [])[0])
      }, 500)
    }
  }, { immediate: true })
</script>

<style lang="scss" scoped>

  .__ci {
    width: 100%;
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    cursor:pointer;

    .__t {
      text-decoration: none;
      transition: all .2s;
    }

    &:hover {
      .__t {
        text-decoration: underline;
      }
    }
  }
  .__list {
    display: grid;
    grid-template-rows: auto 1fr auto;

    > div {
      &:nth-child(2) {
        height: 100%;
        max-height: min(80vh, 500px);
        overflow-y: scroll
      }
    }
  }
</style>
