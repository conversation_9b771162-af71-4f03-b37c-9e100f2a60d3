// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type {Params} from '@feathersjs/feathers'
import {MongoDBService} from '@feathersjs/mongodb'
import type {MongoDBAdapterParams, MongoDBAdapterOptions} from '@feathersjs/mongodb'

import type {Application} from '../../declarations.js'
import type {Orgs, OrgsData, OrgsPatch, OrgsQuery} from './orgs.schema.js'

export type {Orgs, OrgsData, OrgsPatch, OrgsQuery}

export interface OrgsParams extends MongoDBAdapterParams<OrgsQuery> {
}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class OrgsService<ServiceParams extends Params = OrgsParams> extends MongoDBService<
    Orgs,
    OrgsData,
    OrgsParams,
    OrgsPatch
> {
    async get(id: any, params?: ServiceParams) {
        console.log('=== ORGS SERVICE GET DEBUG ===');
        console.log('Service get called with ID:', id);
        console.log('ID type:', typeof id);
        console.log('ID constructor:', id?.constructor?.name);
        console.log('Params:', params);

        try {
            const result = await super.get(id, params);
            console.log('Service get SUCCESS - found record:', !!result);
            console.log('Result ID:', result?._id);
            return result;
        } catch (error) {
            console.log('Service get ERROR:', error.message);
            console.log('Error type:', error.constructor.name);
            throw error;
        }
    }
}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
    return {
        paginate: app.get('paginate'),
        multi: true,
        Model: app.get('mongodbClient')
            .then((db) => db.collection('orgs'))
            .then((collection) => {
                // collection.createIndex({name: 1}, {unique: true});
                return collection
            }),
        operators: ['$regex', '$options']
    }
}
